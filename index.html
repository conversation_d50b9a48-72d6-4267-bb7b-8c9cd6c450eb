<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lumina Photography Studio - Professional Photography Services</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#1a1a1a',
                        secondary: '#f8f8f8',
                        accent: '#d4af37',
                        muted: '#6b7280'
                    },
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                        'serif': ['Playfair Display', 'serif'],
                        'arabic': ['Cairo', 'Noto Sans Arabic', 'sans-serif']
                    },
                    animation: {
                        'fade-in-up': 'fadeInUp 0.8s ease-out',
                        'fade-in-left': 'fadeInLeft 0.8s ease-out',
                        'fade-in-right': 'fadeInRight 0.8s ease-out',
                        'bounce-slow': 'bounce 2s infinite',
                        'pulse-slow': 'pulse 3s infinite',
                        'counter': 'counter 2s ease-out',
                        'float': 'float 3s ease-in-out infinite'
                    }
                }
            }
        }
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Playfair+Display:wght@400;500;600;700;800;900&family=Cairo:wght@300;400;500;600;700;800;900&family=Noto+Sans+Arabic:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary: #1a1a1a;
            --secondary: #f8f8f8;
            --accent: #d4af37;
            --muted: #6b7280;
            --bg-primary: #ffffff;
            --bg-secondary: #f8f8f8;
            --text-primary: #1a1a1a;
            --text-secondary: #6b7280;
            --glass-bg: rgba(255,255,255,0.1);
            --glass-border: rgba(255,255,255,0.2);
            --shadow-light: 0 8px 32px rgba(0,0,0,0.1);
            --shadow-heavy: 0 20px 40px rgba(0,0,0,0.15);
        }

        [data-theme="dark"] {
            --primary: #ffffff;
            --secondary: #1a1a1a;
            --accent: #d4af37;
            --muted: #9ca3af;
            --bg-primary: #0f0f0f;
            --bg-secondary: #1a1a1a;
            --text-primary: #ffffff;
            --text-secondary: #9ca3af;
            --glass-bg: rgba(0,0,0,0.5);
            --glass-border: rgba(255,255,255,0.1);
            --shadow-light: 0 8px 32px rgba(0,0,0,0.4);
            --shadow-heavy: 0 20px 40px rgba(0,0,0,0.6);
        }

        [data-theme="dark"] .bg-secondary {
            background-color: var(--bg-secondary) !important;
        }

        [data-theme="dark"] .text-primary {
            color: var(--text-primary) !important;
        }

        [data-theme="dark"] .text-muted {
            color: var(--text-secondary) !important;
        }

        [data-theme="dark"] .gallery-item,
        [data-theme="dark"] .testimonial-card,
        [data-theme="dark"] .pricing-card {
            background-color: #1f1f1f !important;
            border-color: rgba(255,255,255,0.1) !important;
        }

        [data-theme="dark"] .nav-blur {
            background: rgba(0,0,0,0.8) !important;
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        [data-theme="dark"] #mobile-menu {
            background: rgba(0,0,0,0.9) !important;
            backdrop-filter: blur(20px);
        }

        [data-theme="dark"] input,
        [data-theme="dark"] textarea,
        [data-theme="dark"] select {
            background-color: #2a2a2a !important;
            border-color: rgba(255,255,255,0.2) !important;
            color: #ffffff !important;
        }

        [data-theme="dark"] input::placeholder,
        [data-theme="dark"] textarea::placeholder {
            color: #9ca3af !important;
        }

        [data-theme="dark"] .loading-skeleton {
            background: linear-gradient(90deg, #2a2a2a 25%, #3a3a3a 50%, #2a2a2a 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }

        /* Contact Section Styling */
        .contact-section {
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            color: white;
        }

        [data-theme="dark"] .contact-section {
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
        }

        .contact-title {
            color: white;
        }

        .contact-subtitle {
            color: #d1d5db;
        }

        [data-theme="dark"] .contact-title {
            color: #ffffff;
        }

        [data-theme="dark"] .contact-subtitle {
            color: #9ca3af;
        }

        /* Contact Form Styling */
        .contact-form {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        [data-theme="dark"] .contact-form {
            background: rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .contact-input {
            background-color: rgba(255, 255, 255, 0.1);
            border-color: rgba(255, 255, 255, 0.3);
            color: white;
        }

        .contact-input::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }

        [data-theme="dark"] .contact-input {
            background-color: rgba(0, 0, 0, 0.3);
            border-color: rgba(255, 255, 255, 0.2);
            color: #ffffff;
        }

        [data-theme="dark"] .contact-input::placeholder {
            color: #9ca3af;
        }

        /* Footer Styling */
        .footer-section {
            background-color: #000000;
            color: white;
        }

        [data-theme="dark"] .footer-section {
            background-color: #000000;
        }

        .footer-brand {
            color: white;
        }

        .footer-text {
            color: #9ca3af;
        }

        [data-theme="dark"] .footer-brand {
            color: #ffffff;
        }

        [data-theme="dark"] .footer-text {
            color: #6b7280;
        }

        /* Additional Dark Mode Fixes */
        [data-theme="dark"] .bg-gradient-to-br {
            background: linear-gradient(to bottom right, #1a1a1a, #0f0f0f) !important;
        }

        [data-theme="dark"] .bg-gradient-to-r {
            background: linear-gradient(to right, #1a1a1a, #0f0f0f) !important;
        }

        [data-theme="dark"] h1,
        [data-theme="dark"] h2,
        [data-theme="dark"] h3,
        [data-theme="dark"] h4 {
            color: #ffffff !important;
        }

        [data-theme="dark"] p {
            color: #d1d5db;
        }

        [data-theme="dark"] .portfolio-filter {
            color: #d1d5db;
            border: 1px solid rgba(255,255,255,0.2);
        }

        [data-theme="dark"] .portfolio-filter:hover {
            background-color: rgba(255,255,255,0.1);
        }

        [data-theme="dark"] .portfolio-filter.active {
            background-color: var(--accent) !important;
            color: #1a1a1a !important;
        }

        /* Ensure proper text colors in dark mode */
        [data-theme="dark"] .text-accent {
            color: var(--accent) !important;
        }

        [data-theme="dark"] .text-white {
            color: #ffffff !important;
        }

        [data-theme="dark"] .text-gray-300 {
            color: #d1d5db !important;
        }

        [data-theme="dark"] .text-gray-400 {
            color: #9ca3af !important;
        }

        /* About Section Dark Mode Fixes */
        [data-theme="dark"] #about h2 {
            color: #ffffff !important;
        }

        [data-theme="dark"] #about p {
            color: #d1d5db !important;
        }

        [data-theme="dark"] #about .text-muted {
            color: #9ca3af !important;
        }

        [data-theme="dark"] #about h3 {
            color: #ffffff !important;
        }

        [data-theme="dark"] #about .counter-number {
            color: var(--accent) !important;
        }

        [data-theme="dark"] #about .certifications-list span {
            color: #d1d5db !important;
        }

        [data-theme="dark"] #about .floating-badge {
            background-color: var(--accent) !important;
            color: #1a1a1a !important;
        }

        /* Certifications styling */
        .certifications-list {
            list-style: none;
            padding: 0;
        }

        .certifications-list .flex {
            margin-bottom: 0.5rem;
        }

        .certifications-list span {
            color: var(--text-secondary);
            transition: color 0.3s ease;
        }

        [data-theme="dark"] .certifications-list span {
            color: #d1d5db !important;
        }

        /* Floating badge styling */
        .floating-badge {
            background-color: var(--accent);
            color: var(--primary);
            transition: all 0.3s ease;
        }

        [data-theme="dark"] .floating-badge {
            background-color: var(--accent) !important;
            color: #1a1a1a !important;
        }

        html { scroll-behavior: smooth; }
        body { background-color: var(--bg-primary); color: var(--text-primary); transition: all 0.3s ease; }

        .hero-bg {
            background: linear-gradient(135deg, rgba(26,26,26,0.7), rgba(26,26,26,0.5)), url('https://images.unsplash.com/photo-1606983340126-99ab4feaa64a?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
        }

        .parallax { background-attachment: fixed; background-position: center; background-repeat: no-repeat; background-size: cover; }

        .fade-in { opacity: 0; transform: translateY(30px); transition: all 0.8s ease; }
        .fade-in.visible { opacity: 1; transform: translateY(0); }
        .fade-in-left { opacity: 0; transform: translateX(-30px); transition: all 0.8s ease; }
        .fade-in-left.visible { opacity: 1; transform: translateX(0); }
        .fade-in-right { opacity: 0; transform: translateX(30px); transition: all 0.8s ease; }
        .fade-in-right.visible { opacity: 1; transform: translateX(0); }

        .hover-scale { transition: transform 0.3s ease; }
        .hover-scale:hover { transform: scale(1.05); }

        .glass {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            box-shadow: var(--shadow-light);
        }

        .nav-blur {
            backdrop-filter: blur(20px);
            background: var(--glass-bg);
            border-bottom: 1px solid var(--glass-border);
        }

        .gallery-item {
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            background: var(--bg-primary);
            box-shadow: var(--shadow-light);
        }
        .gallery-item:hover {
            transform: translateY(-15px) scale(1.02);
            box-shadow: var(--shadow-heavy);
        }

        .stagger-animation { animation-delay: calc(var(--stagger) * 0.1s); }

        @keyframes fadeInUp { from { opacity: 0; transform: translateY(30px); } to { opacity: 1; transform: translateY(0); } }
        @keyframes fadeInLeft { from { opacity: 0; transform: translateX(-30px); } to { opacity: 1; transform: translateX(0); } }
        @keyframes fadeInRight { from { opacity: 0; transform: translateX(30px); } to { opacity: 1; transform: translateX(0); } }
        @keyframes float { 0%, 100% { transform: translateY(0px); } 50% { transform: translateY(-10px); } }
        @keyframes counter { from { opacity: 0; transform: scale(0.5); } to { opacity: 1; transform: scale(1); } }

        .loading-skeleton { background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; animation: loading 1.5s infinite; }
        @keyframes loading { 0% { background-position: 200% 0; } 100% { background-position: -200% 0; } }

        .rtl { direction: rtl; text-align: right; }
        .rtl .flex { flex-direction: row-reverse; }
        .rtl .space-x-4 > * + * { margin-left: 0; margin-right: 1rem; }
        .rtl .space-x-6 > * + * { margin-left: 0; margin-right: 1.5rem; }
        .rtl .space-x-8 > * + * { margin-left: 0; margin-right: 2rem; }

        .progress-bar { width: 0%; transition: width 2s ease-out; }
        .progress-bar.animate { width: 100%; }

        .testimonial-card {
            background: var(--bg-primary);
            border: 1px solid var(--glass-border);
            transition: all 0.3s ease;
        }
        .testimonial-card:hover { transform: translateY(-5px); box-shadow: var(--shadow-heavy); }

        .pricing-card {
            background: var(--bg-primary);
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }
        .pricing-card:hover { border-color: var(--accent); transform: scale(1.05); }
        .pricing-card.featured { border-color: var(--accent); transform: scale(1.05); }


    </style>
</head>
<body class="font-sans text-primary bg-white">
    <!-- Navigation -->
    <nav id="navbar" class="fixed top-0 w-full z-50 transition-all duration-300 py-4">
        <div class="container mx-auto px-6 flex justify-between items-center">
            <div class="text-2xl font-serif font-bold" data-translate="brand">Lumina</div>
            <div class="hidden md:flex space-x-8 items-center">
                <a href="#home" class="nav-link hover:text-accent transition-colors" data-translate="nav.home">Home</a>
                <a href="#services" class="nav-link hover:text-accent transition-colors" data-translate="nav.services">Services</a>
                <a href="#portfolio" class="nav-link hover:text-accent transition-colors" data-translate="nav.portfolio">Portfolio</a>
                <a href="#about" class="nav-link hover:text-accent transition-colors" data-translate="nav.about">About</a>
                <a href="#contact" class="nav-link hover:text-accent transition-colors" data-translate="nav.contact">Contact</a>

                <!-- Theme Toggle -->
                <button id="theme-toggle" class="p-2 rounded-full glass hover:bg-accent hover:text-primary transition-all">
                    <i class="fas fa-moon" id="theme-icon"></i>
                </button>

                <!-- Language Toggle -->
                <button id="lang-toggle" class="px-3 py-1 rounded-full glass hover:bg-accent hover:text-primary transition-all text-sm font-semibold">
                    <span id="lang-text">عربي</span>
                </button>
            </div>
            <button id="mobile-menu-btn" class="md:hidden text-2xl">
                <i class="fas fa-bars"></i>
            </button>
        </div>
        <!-- Mobile Menu -->
        <div id="mobile-menu" class="md:hidden hidden glass">
            <div class="px-6 py-4 space-y-4">
                <a href="#home" class="block hover:text-accent transition-colors" data-translate="nav.home">Home</a>
                <a href="#services" class="block hover:text-accent transition-colors" data-translate="nav.services">Services</a>
                <a href="#portfolio" class="block hover:text-accent transition-colors" data-translate="nav.portfolio">Portfolio</a>
                <a href="#about" class="block hover:text-accent transition-colors" data-translate="nav.about">About</a>
                <a href="#contact" class="block hover:text-accent transition-colors" data-translate="nav.contact">Contact</a>
                <div class="flex space-x-4 pt-4">
                    <button id="mobile-theme-toggle" class="p-2 rounded-full glass hover:bg-accent hover:text-primary transition-all">
                        <i class="fas fa-moon" id="mobile-theme-icon"></i>
                    </button>
                    <button id="mobile-lang-toggle" class="px-3 py-1 rounded-full glass hover:bg-accent hover:text-primary transition-all text-sm font-semibold">
                        <span id="mobile-lang-text">عربي</span>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero-bg parallax min-h-screen flex items-center justify-center text-white relative overflow-hidden">
        <div class="absolute inset-0 bg-gradient-to-r from-black/50 via-transparent to-black/50"></div>
        <div class="text-center px-6 max-w-4xl relative z-10">
            <div class="mb-6">
                <div class="inline-block animate-float">
                    <i class="fas fa-camera text-accent text-6xl mb-4"></i>
                </div>
            </div>
            <h1 class="text-5xl md:text-7xl font-serif font-bold mb-6 fade-in">
                <span data-translate="hero.title1">Capturing Life's</span><br>
                <span class="text-accent" data-translate="hero.title2">Perfect Moments</span>
            </h1>
            <p class="text-xl md:text-2xl mb-8 fade-in font-light" data-translate="hero.subtitle">
                Professional photography services that transform your precious memories into timeless art
            </p>
            <div class="space-x-4 fade-in">
                <button class="bg-accent text-primary px-8 py-4 rounded-full font-semibold hover:bg-yellow-400 transition-all hover-scale glass-button" data-translate="hero.cta1">
                    View Portfolio
                </button>
                <button class="border-2 border-white px-8 py-4 rounded-full font-semibold hover:bg-white hover:text-primary transition-all glass" data-translate="hero.cta2">
                    Book Session
                </button>
            </div>
        </div>

        <!-- Floating Elements -->
        <div class="absolute top-20 left-10 animate-float" style="animation-delay: 0.5s;">
            <div class="w-4 h-4 bg-accent rounded-full opacity-60"></div>
        </div>
        <div class="absolute bottom-32 right-16 animate-float" style="animation-delay: 1s;">
            <div class="w-6 h-6 bg-white rounded-full opacity-40"></div>
        </div>
        <div class="absolute top-1/3 right-20 animate-float" style="animation-delay: 1.5s;">
            <div class="w-3 h-3 bg-accent rounded-full opacity-50"></div>
        </div>
    </section>

    <!-- Services Section -->
    <section id="services" class="py-20 bg-secondary">
        <div class="container mx-auto px-6">
            <div class="text-center mb-16 fade-in">
                <h2 class="text-4xl md:text-5xl font-serif font-bold mb-4" data-translate="services.title">Our Services</h2>
                <p class="text-xl text-muted max-w-2xl mx-auto" data-translate="services.subtitle">
                    From intimate portraits to grand celebrations, we specialize in capturing the essence of every moment
                </p>
            </div>

            <div class="grid md:grid-cols-3 gap-8">
                <div class="gallery-item bg-white rounded-2xl overflow-hidden shadow-lg fade-in" style="--stagger: 1">
                    <div class="h-64 bg-cover bg-center" style="background-image: url('https://images.unsplash.com/photo-1511285560929-80b456fea0bc?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80')"></div>
                    <div class="p-8">
                        <h3 class="text-2xl font-serif font-semibold mb-4" data-translate="services.wedding.title">Wedding Photography</h3>
                        <p class="text-muted mb-6" data-translate="services.wedding.description">Elegant and timeless wedding photography that captures the magic of your special day with artistic flair.</p>
                        <a href="#" class="text-accent font-semibold hover:underline">Learn More →</a>
                    </div>
                </div>

                <div class="gallery-item bg-white rounded-2xl overflow-hidden shadow-lg fade-in" style="--stagger: 2">
                    <div class="h-64 bg-cover bg-center" style="background-image: url('https://images.unsplash.com/photo-1544005313-94ddf0286df2?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80')"></div>
                    <div class="p-8">
                        <h3 class="text-2xl font-serif font-semibold mb-4" data-translate="services.portrait.title">Portrait Sessions</h3>
                        <p class="text-muted mb-6" data-translate="services.portrait.description">Professional portraits that reveal your unique personality and style in stunning, magazine-quality images.</p>
                        <a href="#" class="text-accent font-semibold hover:underline">Learn More →</a>
                    </div>
                </div>

                <div class="gallery-item bg-white rounded-2xl overflow-hidden shadow-lg fade-in" style="--stagger: 3">
                    <div class="h-64 bg-cover bg-center" style="background-image: url('https://images.unsplash.com/photo-1492691527719-9d1e07e534b4?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80')"></div>
                    <div class="p-8">
                        <h3 class="text-2xl font-serif font-semibold mb-4" data-translate="services.event.title">Event Photography</h3>
                        <p class="text-muted mb-6" data-translate="services.event.description">Comprehensive event coverage that documents every important moment with creativity and professionalism.</p>
                        <a href="#" class="text-accent font-semibold hover:underline">Learn More →</a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Portfolio Gallery Section -->
    <section id="portfolio" class="py-20 bg-gradient-to-br from-gray-50 to-white">
        <div class="container mx-auto px-6">
            <div class="text-center mb-16 fade-in">
                <h2 class="text-4xl md:text-5xl font-serif font-bold mb-4" data-translate="portfolio.title">Our Portfolio</h2>
                <p class="text-xl text-muted max-w-2xl mx-auto" data-translate="portfolio.subtitle">
                    Explore our collection of stunning photography that captures the beauty and emotion of life's most precious moments
                </p>
            </div>

            <!-- Portfolio Filter -->
            <div class="flex justify-center mb-12 fade-in">
                <div class="glass rounded-full p-2">
                    <button class="portfolio-filter active px-6 py-2 rounded-full transition-all" data-filter="all" data-translate="portfolio.filter.all">All</button>
                    <button class="portfolio-filter px-6 py-2 rounded-full transition-all" data-filter="wedding" data-translate="portfolio.filter.wedding">Wedding</button>
                    <button class="portfolio-filter px-6 py-2 rounded-full transition-all" data-filter="portrait" data-translate="portfolio.filter.portrait">Portrait</button>
                    <button class="portfolio-filter px-6 py-2 rounded-full transition-all" data-filter="event" data-translate="portfolio.filter.event">Event</button>
                </div>
            </div>

            <!-- Portfolio Grid -->
            <div class="grid md:grid-cols-3 lg:grid-cols-4 gap-6" id="portfolio-grid">
                <div class="portfolio-item wedding fade-in" style="--stagger: 1">
                    <div class="relative overflow-hidden rounded-2xl group cursor-pointer">
                        <img src="https://images.unsplash.com/photo-1519741497674-611481863552?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80" alt="Wedding Photography" class="w-full h-64 object-cover transition-transform duration-500 group-hover:scale-110">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <div class="absolute bottom-4 left-4 text-white">
                                <h4 class="font-semibold">Elegant Wedding</h4>
                                <p class="text-sm opacity-80">Sarah & Michael</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="portfolio-item portrait fade-in" style="--stagger: 2">
                    <div class="relative overflow-hidden rounded-2xl group cursor-pointer">
                        <img src="https://images.unsplash.com/photo-1531746020798-e6953c6e8e04?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80" alt="Portrait Photography" class="w-full h-64 object-cover transition-transform duration-500 group-hover:scale-110">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <div class="absolute bottom-4 left-4 text-white">
                                <h4 class="font-semibold">Professional Portrait</h4>
                                <p class="text-sm opacity-80">Corporate Session</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="portfolio-item event fade-in" style="--stagger: 3">
                    <div class="relative overflow-hidden rounded-2xl group cursor-pointer">
                        <img src="https://images.unsplash.com/photo-1464366400600-7168b8af9bc3?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80" alt="Event Photography" class="w-full h-64 object-cover transition-transform duration-500 group-hover:scale-110">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <div class="absolute bottom-4 left-4 text-white">
                                <h4 class="font-semibold">Corporate Event</h4>
                                <p class="text-sm opacity-80">Annual Gala</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="portfolio-item wedding fade-in" style="--stagger: 4">
                    <div class="relative overflow-hidden rounded-2xl group cursor-pointer">
                        <img src="https://images.unsplash.com/photo-1606216794074-735e91aa2c92?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80" alt="Wedding Photography" class="w-full h-64 object-cover transition-transform duration-500 group-hover:scale-110">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <div class="absolute bottom-4 left-4 text-white">
                                <h4 class="font-semibold">Garden Wedding</h4>
                                <p class="text-sm opacity-80">Emma & James</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="portfolio-item portrait fade-in" style="--stagger: 5">
                    <div class="relative overflow-hidden rounded-2xl group cursor-pointer">
                        <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80" alt="Portrait Photography" class="w-full h-64 object-cover transition-transform duration-500 group-hover:scale-110">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <div class="absolute bottom-4 left-4 text-white">
                                <h4 class="font-semibold">Creative Portrait</h4>
                                <p class="text-sm opacity-80">Artist Session</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="portfolio-item event fade-in" style="--stagger: 6">
                    <div class="relative overflow-hidden rounded-2xl group cursor-pointer">
                        <img src="https://images.unsplash.com/photo-1511578314322-379afb476865?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80" alt="Event Photography" class="w-full h-64 object-cover transition-transform duration-500 group-hover:scale-110">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <div class="absolute bottom-4 left-4 text-white">
                                <h4 class="font-semibold">Birthday Celebration</h4>
                                <p class="text-sm opacity-80">Family Event</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="portfolio-item wedding fade-in" style="--stagger: 7">
                    <div class="relative overflow-hidden rounded-2xl group cursor-pointer">
                        <img src="https://images.unsplash.com/photo-1583939003579-730e3918a45a?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80" alt="Wedding Photography" class="w-full h-64 object-cover transition-transform duration-500 group-hover:scale-110">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <div class="absolute bottom-4 left-4 text-white">
                                <h4 class="font-semibold">Beach Wedding</h4>
                                <p class="text-sm opacity-80">Lisa & David</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="portfolio-item portrait fade-in" style="--stagger: 8">
                    <div class="relative overflow-hidden rounded-2xl group cursor-pointer">
                        <img src="https://images.unsplash.com/photo-1494790108755-2616c9c0e8e3?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80" alt="Portrait Photography" class="w-full h-64 object-cover transition-transform duration-500 group-hover:scale-110">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <div class="absolute bottom-4 left-4 text-white">
                                <h4 class="font-semibold">Fashion Portrait</h4>
                                <p class="text-sm opacity-80">Model Shoot</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="text-center mt-12 fade-in">
                <button class="bg-accent text-primary px-8 py-4 rounded-full font-semibold hover:bg-yellow-400 transition-all hover-scale" data-translate="portfolio.viewMore">
                    View More Work
                </button>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="py-20">
        <div class="container mx-auto px-6">
            <div class="grid md:grid-cols-2 gap-16 items-center">
                <div class="fade-in-left">
                    <h2 class="text-4xl md:text-5xl font-serif font-bold mb-6" data-translate="about.title">Meet the Artist</h2>
                    <p class="text-lg text-muted mb-6" data-translate="about.description1">
                        With over a decade of experience in professional photography, I specialize in creating stunning visual narratives that capture the authentic emotions and beauty of life's most precious moments.
                    </p>
                    <p class="text-lg text-muted mb-8" data-translate="about.description2">
                        My approach combines technical excellence with artistic vision, ensuring that every photograph tells a unique story and becomes a treasured memory for years to come.
                    </p>

                    <!-- Stats with animated counters -->
                    <div class="grid grid-cols-3 gap-6 mb-8">
                        <div class="text-center">
                            <div class="text-3xl font-bold text-accent counter-number" data-target="500">0</div>
                            <div class="text-muted" data-translate="about.stats.clients">Happy Clients</div>
                        </div>
                        <div class="text-center">
                            <div class="text-3xl font-bold text-accent counter-number" data-target="10">0</div>
                            <div class="text-muted" data-translate="about.stats.experience">Years Experience</div>
                        </div>
                        <div class="text-center">
                            <div class="text-3xl font-bold text-accent counter-number" data-target="50">0</div>
                            <div class="text-muted" data-translate="about.stats.awards">Awards Won</div>
                        </div>
                    </div>

                    <!-- Certifications and Awards -->
                    <div class="space-y-4">
                        <h3 class="text-xl font-serif font-semibold mb-4">Certifications & Awards</h3>
                        <div class="certifications-list">
                            <div class="flex items-center space-x-3 mb-2">
                                <i class="fas fa-award text-accent"></i>
                                <span>Professional Photographers of America (PPA) Certified</span>
                            </div>
                            <div class="flex items-center space-x-3 mb-2">
                                <i class="fas fa-trophy text-accent"></i>
                                <span>Wedding Photography Excellence Award 2023</span>
                            </div>
                            <div class="flex items-center space-x-3 mb-2">
                                <i class="fas fa-medal text-accent"></i>
                                <span>International Photography Society Member</span>
                            </div>
                            <div class="flex items-center space-x-3">
                                <i class="fas fa-star text-accent"></i>
                                <span>Featured in Photography Masters Magazine</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="fade-in-right">
                    <div class="relative">
                        <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80"
                             alt="Professional Photographer"
                             class="rounded-2xl shadow-2xl hover-scale w-full">
                        <div class="absolute -bottom-6 -right-6 floating-badge p-6 rounded-2xl glass">
                            <div class="text-2xl font-bold">10+</div>
                            <div class="text-sm">Years of Excellence</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section class="py-20 bg-gradient-to-r from-gray-50 to-gray-100">
        <div class="container mx-auto px-6">
            <div class="text-center mb-16 fade-in">
                <h2 class="text-4xl md:text-5xl font-serif font-bold mb-4" data-translate="testimonials.title">What Our Clients Say</h2>
                <p class="text-xl text-muted max-w-2xl mx-auto" data-translate="testimonials.subtitle">
                    Don't just take our word for it - hear from the amazing couples and families we've had the privilege to work with
                </p>
            </div>

            <div class="grid md:grid-cols-3 gap-8">
                <div class="testimonial-card p-8 rounded-2xl shadow-lg fade-in" style="--stagger: 1">
                    <div class="flex items-center mb-4">
                        <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80" alt="Client" class="w-16 h-16 rounded-full object-cover mr-4">
                        <div>
                            <h4 class="font-semibold text-lg">Sarah Johnson</h4>
                            <div class="flex text-accent">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                        </div>
                    </div>
                    <p class="text-muted italic mb-4" data-translate="testimonials.review1">
                        "Absolutely stunning work! The photographer captured every precious moment of our wedding day with such artistry and professionalism. We couldn't be happier with the results."
                    </p>
                    <div class="text-sm text-muted" data-translate="testimonials.service1">Wedding Photography</div>
                </div>

                <div class="testimonial-card p-8 rounded-2xl shadow-lg fade-in" style="--stagger: 2">
                    <div class="flex items-center mb-4">
                        <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80" alt="Client" class="w-16 h-16 rounded-full object-cover mr-4">
                        <div>
                            <h4 class="font-semibold text-lg">Michael Chen</h4>
                            <div class="flex text-accent">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                        </div>
                    </div>
                    <p class="text-muted italic mb-4" data-translate="testimonials.review2">
                        "Professional, creative, and incredibly talented. The portrait session exceeded all our expectations. The attention to detail and artistic vision is remarkable."
                    </p>
                    <div class="text-sm text-muted" data-translate="testimonials.service2">Corporate Portraits</div>
                </div>

                <div class="testimonial-card p-8 rounded-2xl shadow-lg fade-in" style="--stagger: 3">
                    <div class="flex items-center mb-4">
                        <img src="https://images.unsplash.com/photo-1494790108755-2616c9c0e8e3?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80" alt="Client" class="w-16 h-16 rounded-full object-cover mr-4">
                        <div>
                            <h4 class="font-semibold text-lg">Emma Rodriguez</h4>
                            <div class="flex text-accent">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                        </div>
                    </div>
                    <p class="text-muted italic mb-4" data-translate="testimonials.review3">
                        "From the initial consultation to the final delivery, the entire experience was seamless. The photos are absolutely breathtaking and capture the essence of our family perfectly."
                    </p>
                    <div class="text-sm text-muted" data-translate="testimonials.service3">Family Photography</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Pricing Section -->
    <section class="py-20">
        <div class="container mx-auto px-6">
            <div class="text-center mb-16 fade-in">
                <h2 class="text-4xl md:text-5xl font-serif font-bold mb-4" data-translate="pricing.title">Photography Packages</h2>
                <p class="text-xl text-muted max-w-2xl mx-auto" data-translate="pricing.subtitle">
                    Choose the perfect package for your special occasion. All packages include professional editing and digital gallery access.
                </p>
            </div>

            <div class="grid md:grid-cols-3 gap-8">
                <div class="pricing-card p-8 rounded-2xl shadow-lg fade-in" style="--stagger: 1">
                    <div class="text-center mb-6">
                        <h3 class="text-2xl font-serif font-semibold mb-2" data-translate="pricing.essential.title">Essential</h3>
                        <div class="text-4xl font-bold text-accent mb-2">$599</div>
                        <p class="text-muted" data-translate="pricing.essential.duration">2 Hour Session</p>
                    </div>
                    <ul class="space-y-3 mb-8">
                        <li class="flex items-center"><i class="fas fa-check text-accent mr-3"></i><span data-translate="pricing.essential.feature1">50 Edited Photos</span></li>
                        <li class="flex items-center"><i class="fas fa-check text-accent mr-3"></i><span data-translate="pricing.essential.feature2">Online Gallery</span></li>
                        <li class="flex items-center"><i class="fas fa-check text-accent mr-3"></i><span data-translate="pricing.essential.feature3">High-Resolution Downloads</span></li>
                        <li class="flex items-center"><i class="fas fa-check text-accent mr-3"></i><span data-translate="pricing.essential.feature4">Print Release</span></li>
                    </ul>
                    <button class="w-full bg-gray-200 text-primary py-3 rounded-lg font-semibold hover:bg-gray-300 transition-colors" data-translate="pricing.selectPackage">
                        Select Package
                    </button>
                </div>

                <div class="pricing-card featured p-8 rounded-2xl shadow-2xl fade-in relative" style="--stagger: 2">
                    <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
                        <span class="bg-accent text-primary px-4 py-1 rounded-full text-sm font-semibold" data-translate="pricing.popular">Most Popular</span>
                    </div>
                    <div class="text-center mb-6">
                        <h3 class="text-2xl font-serif font-semibold mb-2" data-translate="pricing.premium.title">Premium</h3>
                        <div class="text-4xl font-bold text-accent mb-2">$999</div>
                        <p class="text-muted" data-translate="pricing.premium.duration">4 Hour Session</p>
                    </div>
                    <ul class="space-y-3 mb-8">
                        <li class="flex items-center"><i class="fas fa-check text-accent mr-3"></i><span data-translate="pricing.premium.feature1">100 Edited Photos</span></li>
                        <li class="flex items-center"><i class="fas fa-check text-accent mr-3"></i><span data-translate="pricing.premium.feature2">Online Gallery</span></li>
                        <li class="flex items-center"><i class="fas fa-check text-accent mr-3"></i><span data-translate="pricing.premium.feature3">High-Resolution Downloads</span></li>
                        <li class="flex items-center"><i class="fas fa-check text-accent mr-3"></i><span data-translate="pricing.premium.feature4">Print Release</span></li>
                        <li class="flex items-center"><i class="fas fa-check text-accent mr-3"></i><span data-translate="pricing.premium.feature5">USB Drive</span></li>
                        <li class="flex items-center"><i class="fas fa-check text-accent mr-3"></i><span data-translate="pricing.premium.feature6">Engagement Session</span></li>
                    </ul>
                    <button class="w-full bg-accent text-primary py-3 rounded-lg font-semibold hover:bg-yellow-400 transition-colors" data-translate="pricing.selectPackage">
                        Select Package
                    </button>
                </div>

                <div class="pricing-card p-8 rounded-2xl shadow-lg fade-in" style="--stagger: 3">
                    <div class="text-center mb-6">
                        <h3 class="text-2xl font-serif font-semibold mb-2" data-translate="pricing.luxury.title">Luxury</h3>
                        <div class="text-4xl font-bold text-accent mb-2">$1599</div>
                        <p class="text-muted" data-translate="pricing.luxury.duration">Full Day Coverage</p>
                    </div>
                    <ul class="space-y-3 mb-8">
                        <li class="flex items-center"><i class="fas fa-check text-accent mr-3"></i><span data-translate="pricing.luxury.feature1">200+ Edited Photos</span></li>
                        <li class="flex items-center"><i class="fas fa-check text-accent mr-3"></i><span data-translate="pricing.luxury.feature2">Online Gallery</span></li>
                        <li class="flex items-center"><i class="fas fa-check text-accent mr-3"></i><span data-translate="pricing.luxury.feature3">High-Resolution Downloads</span></li>
                        <li class="flex items-center"><i class="fas fa-check text-accent mr-3"></i><span data-translate="pricing.luxury.feature4">Print Release</span></li>
                        <li class="flex items-center"><i class="fas fa-check text-accent mr-3"></i><span data-translate="pricing.luxury.feature5">USB Drive + Photo Album</span></li>
                        <li class="flex items-center"><i class="fas fa-check text-accent mr-3"></i><span data-translate="pricing.luxury.feature6">Engagement Session</span></li>
                        <li class="flex items-center"><i class="fas fa-check text-accent mr-3"></i><span data-translate="pricing.luxury.feature7">Second Photographer</span></li>
                    </ul>
                    <button class="w-full bg-gray-200 text-primary py-3 rounded-lg font-semibold hover:bg-gray-300 transition-colors" data-translate="pricing.selectPackage">
                        Select Package
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="py-20 contact-section">
        <div class="container mx-auto px-6">
            <div class="text-center mb-16 fade-in">
                <h2 class="text-4xl md:text-5xl font-serif font-bold mb-4 contact-title" data-translate="contact.title">Let's Create Together</h2>
                <p class="text-xl contact-subtitle max-w-2xl mx-auto" data-translate="contact.subtitle">
                    Ready to capture your special moments? Get in touch and let's discuss how we can bring your vision to life.
                </p>
            </div>

            <div class="grid md:grid-cols-2 gap-16">
                <div class="fade-in-left">
                    <h3 class="text-2xl font-serif font-semibold mb-6" data-translate="contact.getInTouch">Get In Touch</h3>
                    <div class="space-y-4 mb-8">
                        <div class="flex items-center space-x-4">
                            <i class="fas fa-phone text-accent text-xl"></i>
                            <span class="text-lg">+****************</span>
                        </div>
                        <div class="flex items-center space-x-4">
                            <i class="fas fa-envelope text-accent text-xl"></i>
                            <span class="text-lg"><EMAIL></span>
                        </div>
                        <div class="flex items-center space-x-4">
                            <i class="fas fa-map-marker-alt text-accent text-xl"></i>
                            <span class="text-lg">123 Photography Lane, Creative City, CC 12345</span>
                        </div>
                    </div>
                    <div class="flex space-x-4">
                        <a href="#" class="text-2xl hover:text-accent transition-colors hover-scale"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="text-2xl hover:text-accent transition-colors hover-scale"><i class="fab fa-facebook"></i></a>
                        <a href="#" class="text-2xl hover:text-accent transition-colors hover-scale"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-2xl hover:text-accent transition-colors hover-scale"><i class="fab fa-pinterest"></i></a>
                    </div>
                </div>

                <div class="fade-in-right">
                    <form class="space-y-6 contact-form p-8 rounded-2xl">
                        <div>
                            <input type="text" data-translate="contact.form.name" placeholder="Your Name"
                                   class="contact-input w-full px-4 py-3 rounded-lg border focus:border-accent focus:outline-none transition-colors">
                        </div>
                        <div>
                            <input type="email" data-translate="contact.form.email" placeholder="Your Email"
                                   class="contact-input w-full px-4 py-3 rounded-lg border focus:border-accent focus:outline-none transition-colors">
                        </div>
                        <div>
                            <select class="contact-input w-full px-4 py-3 rounded-lg border focus:border-accent focus:outline-none transition-colors">
                                <option data-translate="contact.form.service">Select Service</option>
                                <option>Wedding Photography</option>
                                <option>Portrait Session</option>
                                <option>Event Photography</option>
                                <option>Other</option>
                            </select>
                        </div>
                        <div>
                            <textarea data-translate="contact.form.message" placeholder="Tell us about your project..." rows="4"
                                      class="contact-input w-full px-4 py-3 rounded-lg border focus:border-accent focus:outline-none transition-colors resize-none"></textarea>
                        </div>
                        <button type="submit"
                                class="w-full bg-accent text-primary py-3 rounded-lg font-semibold hover:bg-yellow-400 transition-all hover-scale" data-translate="contact.form.send">
                            Send Message
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer-section py-8">
        <div class="container mx-auto px-6 text-center">
            <div class="text-2xl font-serif font-bold mb-4 footer-brand" data-translate="brand">Lumina</div>
            <p class="footer-text mb-4">© 2024 Lumina Photography Studio. All rights reserved.</p>
            <p class="footer-text">Crafting memories, one frame at a time.</p>
        </div>
    </footer>

    <script>
        // Translations
        const translations = {
            en: {
                brand: "Lumina",
                nav: {
                    home: "Home",
                    services: "Services",
                    portfolio: "Portfolio",
                    about: "About",
                    contact: "Contact"
                },
                hero: {
                    title1: "Capturing Life's",
                    title2: "Perfect Moments",
                    subtitle: "Professional photography services that transform your precious memories into timeless art",
                    cta1: "View Portfolio",
                    cta2: "Book Session"
                },
                services: {
                    title: "Our Services",
                    subtitle: "From intimate portraits to grand celebrations, we specialize in capturing the essence of every moment",
                    wedding: {
                        title: "Wedding Photography",
                        description: "Elegant and timeless wedding photography that captures the magic of your special day with artistic flair."
                    },
                    portrait: {
                        title: "Portrait Sessions",
                        description: "Professional portraits that reveal your unique personality and style in stunning, magazine-quality images."
                    },
                    event: {
                        title: "Event Photography",
                        description: "Comprehensive event coverage that documents every important moment with creativity and professionalism."
                    }
                },
                portfolio: {
                    title: "Our Portfolio",
                    subtitle: "Explore our collection of stunning photography that captures the beauty and emotion of life's most precious moments",
                    filter: {
                        all: "All",
                        wedding: "Wedding",
                        portrait: "Portrait",
                        event: "Event"
                    },
                    viewMore: "View More Work"
                },
                about: {
                    title: "Meet the Artist",
                    description1: "With over a decade of experience in professional photography, I specialize in creating stunning visual narratives that capture the authentic emotions and beauty of life's most precious moments.",
                    description2: "My approach combines technical excellence with artistic vision, ensuring that every photograph tells a unique story and becomes a treasured memory for years to come.",
                    stats: {
                        clients: "Happy Clients",
                        experience: "Years Experience",
                        awards: "Awards Won"
                    }
                },
                testimonials: {
                    title: "What Our Clients Say",
                    subtitle: "Don't just take our word for it - hear from the amazing couples and families we've had the privilege to work with",
                    review1: "Absolutely stunning work! The photographer captured every precious moment of our wedding day with such artistry and professionalism. We couldn't be happier with the results.",
                    review2: "Professional, creative, and incredibly talented. The portrait session exceeded all our expectations. The attention to detail and artistic vision is remarkable.",
                    review3: "From the initial consultation to the final delivery, the entire experience was seamless. The photos are absolutely breathtaking and capture the essence of our family perfectly.",
                    service1: "Wedding Photography",
                    service2: "Corporate Portraits",
                    service3: "Family Photography"
                },
                pricing: {
                    title: "Photography Packages",
                    subtitle: "Choose the perfect package for your special occasion. All packages include professional editing and digital gallery access.",
                    popular: "Most Popular",
                    selectPackage: "Select Package",
                    essential: {
                        title: "Essential",
                        duration: "2 Hour Session",
                        feature1: "50 Edited Photos",
                        feature2: "Online Gallery",
                        feature3: "High-Resolution Downloads",
                        feature4: "Print Release"
                    },
                    premium: {
                        title: "Premium",
                        duration: "4 Hour Session",
                        feature1: "100 Edited Photos",
                        feature2: "Online Gallery",
                        feature3: "High-Resolution Downloads",
                        feature4: "Print Release",
                        feature5: "USB Drive",
                        feature6: "Engagement Session"
                    },
                    luxury: {
                        title: "Luxury",
                        duration: "Full Day Coverage",
                        feature1: "200+ Edited Photos",
                        feature2: "Online Gallery",
                        feature3: "High-Resolution Downloads",
                        feature4: "Print Release",
                        feature5: "USB Drive + Photo Album",
                        feature6: "Engagement Session",
                        feature7: "Second Photographer"
                    }
                },

                contact: {
                    title: "Let's Create Together",
                    subtitle: "Ready to capture your special moments? Get in touch and let's discuss how we can bring your vision to life.",
                    getInTouch: "Get In Touch",
                    form: {
                        name: "Your Name",
                        email: "Your Email",
                        service: "Select Service",
                        message: "Tell us about your project...",
                        send: "Send Message"
                    }
                }
            },
            ar: {
                brand: "لومينا",
                nav: {
                    home: "الرئيسية",
                    services: "الخدمات",
                    portfolio: "معرض الأعمال",
                    about: "من نحن",
                    contact: "اتصل بنا"
                },
                hero: {
                    title1: "نلتقط لحظات الحياة",
                    title2: "المثالية",
                    subtitle: "خدمات التصوير الاحترافية التي تحول ذكرياتك الثمينة إلى فن خالد",
                    cta1: "عرض المعرض",
                    cta2: "احجز جلسة"
                },
                services: {
                    title: "خدماتنا",
                    subtitle: "من الصور الشخصية الحميمة إلى الاحتفالات الكبرى، نتخصص في التقاط جوهر كل لحظة",
                    wedding: {
                        title: "تصوير الأعراس",
                        description: "تصوير أعراس أنيق وخالد يلتقط سحر يومك المميز بذوق فني راقي."
                    },
                    portrait: {
                        title: "جلسات التصوير الشخصي",
                        description: "صور شخصية احترافية تكشف عن شخصيتك الفريدة وأسلوبك في صور مذهلة بجودة المجلات."
                    },
                    event: {
                        title: "تصوير الفعاليات",
                        description: "تغطية شاملة للفعاليات توثق كل لحظة مهمة بإبداع واحترافية."
                    }
                },
                portfolio: {
                    title: "معرض أعمالنا",
                    subtitle: "استكشف مجموعتنا من الصور المذهلة التي تلتقط جمال وعاطفة أثمن لحظات الحياة",
                    filter: {
                        all: "الكل",
                        wedding: "الأعراس",
                        portrait: "الصور الشخصية",
                        event: "الفعاليات"
                    },
                    viewMore: "عرض المزيد من الأعمال"
                },
                about: {
                    title: "تعرف على الفنان",
                    description1: "مع أكثر من عقد من الخبرة في التصوير الاحترافي، أتخصص في إنشاء قصص بصرية مذهلة تلتقط المشاعر الحقيقية وجمال أثمن لحظات الحياة.",
                    description2: "يجمع أسلوبي بين التميز التقني والرؤية الفنية، مما يضمن أن كل صورة تحكي قصة فريدة وتصبح ذكرى عزيزة لسنوات قادمة.",
                    stats: {
                        clients: "عميل سعيد",
                        experience: "سنوات خبرة",
                        awards: "جائزة"
                    }
                },
                testimonials: {
                    title: "ماذا يقول عملاؤنا",
                    subtitle: "لا تأخذ كلامنا فقط - استمع إلى الأزواج والعائلات الرائعة التي كان لنا شرف العمل معها",
                    review1: "عمل مذهل تماماً! التقط المصور كل لحظة ثمينة من يوم زفافنا بمثل هذا الفن والاحترافية. لا يمكن أن نكون أكثر سعادة بالنتائج.",
                    review2: "محترف ومبدع وموهوب بشكل لا يصدق. تجاوزت جلسة التصوير الشخصي كل توقعاتنا. الاهتمام بالتفاصيل والرؤية الفنية رائعة.",
                    review3: "من الاستشارة الأولى إلى التسليم النهائي، كانت التجربة بأكملها سلسة. الصور مذهلة تماماً وتلتقط جوهر عائلتنا بشكل مثالي.",
                    service1: "تصوير الأعراس",
                    service2: "الصور الشخصية للشركات",
                    service3: "تصوير العائلة"
                },
                pricing: {
                    title: "باقات التصوير",
                    subtitle: "اختر الباقة المثالية لمناسبتك الخاصة. جميع الباقات تشمل التحرير الاحترافي والوصول إلى المعرض الرقمي.",
                    popular: "الأكثر شعبية",
                    selectPackage: "اختر الباقة",
                    essential: {
                        title: "الأساسية",
                        duration: "جلسة ساعتين",
                        feature1: "50 صورة محررة",
                        feature2: "معرض إلكتروني",
                        feature3: "تحميل عالي الدقة",
                        feature4: "ترخيص طباعة"
                    },
                    premium: {
                        title: "المميزة",
                        duration: "جلسة 4 ساعات",
                        feature1: "100 صورة محررة",
                        feature2: "معرض إلكتروني",
                        feature3: "تحميل عالي الدقة",
                        feature4: "ترخيص طباعة",
                        feature5: "قرص USB",
                        feature6: "جلسة خطوبة"
                    },
                    luxury: {
                        title: "الفاخرة",
                        duration: "تغطية يوم كامل",
                        feature1: "200+ صورة محررة",
                        feature2: "معرض إلكتروني",
                        feature3: "تحميل عالي الدقة",
                        feature4: "ترخيص طباعة",
                        feature5: "قرص USB + ألبوم صور",
                        feature6: "جلسة خطوبة",
                        feature7: "مصور ثاني"
                    }
                },

                contact: {
                    title: "لنبدع معاً",
                    subtitle: "مستعد لالتقاط لحظاتك الخاصة؟ تواصل معنا ولنناقش كيف يمكننا تحقيق رؤيتك.",
                    getInTouch: "تواصل معنا",
                    form: {
                        name: "اسمك",
                        email: "بريدك الإلكتروني",
                        service: "اختر الخدمة",
                        message: "أخبرنا عن مشروعك...",
                        send: "إرسال الرسالة"
                    }
                }
            }
        };

        // Current language and theme
        let currentLang = 'en';
        let currentTheme = localStorage.getItem('theme') || 'light';

        // Initialize theme
        if (currentTheme === 'dark') {
            document.documentElement.setAttribute('data-theme', 'dark');
            document.getElementById('theme-icon').className = 'fas fa-sun';
            document.getElementById('mobile-theme-icon').className = 'fas fa-sun';
        }

        // Theme toggle functionality
        function toggleTheme() {
            currentTheme = currentTheme === 'light' ? 'dark' : 'light';
            document.documentElement.setAttribute('data-theme', currentTheme);
            localStorage.setItem('theme', currentTheme);

            const icon = currentTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
            document.getElementById('theme-icon').className = icon;
            document.getElementById('mobile-theme-icon').className = icon;
        }

        document.getElementById('theme-toggle').addEventListener('click', toggleTheme);
        document.getElementById('mobile-theme-toggle').addEventListener('click', toggleTheme);

        // Language toggle functionality
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'ar' : 'en';
            updateLanguage();
        }

        function updateLanguage() {
            const isArabic = currentLang === 'ar';

            // Update language toggle text
            document.getElementById('lang-text').textContent = isArabic ? 'English' : 'عربي';
            document.getElementById('mobile-lang-text').textContent = isArabic ? 'English' : 'عربي';

            // Update body direction and font
            document.body.classList.toggle('rtl', isArabic);
            document.body.style.fontFamily = isArabic ? 'Cairo, sans-serif' : 'Inter, sans-serif';

            // Update all translatable elements
            document.querySelectorAll('[data-translate]').forEach(element => {
                const key = element.getAttribute('data-translate');
                const translation = getNestedTranslation(translations[currentLang], key);
                if (translation) {
                    if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
                        element.placeholder = translation;
                    } else {
                        element.textContent = translation;
                    }
                }
            });
        }

        function getNestedTranslation(obj, path) {
            return path.split('.').reduce((current, key) => current && current[key], obj);
        }

        document.getElementById('lang-toggle').addEventListener('click', toggleLanguage);
        document.getElementById('mobile-lang-toggle').addEventListener('click', toggleLanguage);

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({ behavior: 'smooth', block: 'start' });
                }
            });
        });

        // Mobile menu toggle
        const mobileMenuBtn = document.getElementById('mobile-menu-btn');
        const mobileMenu = document.getElementById('mobile-menu');

        mobileMenuBtn.addEventListener('click', () => {
            mobileMenu.classList.toggle('hidden');
        });

        // Close mobile menu when clicking on links
        document.querySelectorAll('#mobile-menu a').forEach(link => {
            link.addEventListener('click', () => {
                mobileMenu.classList.add('hidden');
            });
        });

        // Navbar background on scroll
        const navbar = document.getElementById('navbar');
        window.addEventListener('scroll', () => {
            if (window.scrollY > 100) {
                navbar.classList.add('nav-blur');
            } else {
                navbar.classList.remove('nav-blur');
            }
        });

        // Portfolio filter functionality
        const portfolioFilters = document.querySelectorAll('.portfolio-filter');
        const portfolioItems = document.querySelectorAll('.portfolio-item');

        portfolioFilters.forEach(filter => {
            filter.addEventListener('click', () => {
                // Remove active class from all filters
                portfolioFilters.forEach(f => f.classList.remove('active', 'bg-accent', 'text-primary'));
                // Add active class to clicked filter
                filter.classList.add('active', 'bg-accent', 'text-primary');

                const filterValue = filter.getAttribute('data-filter');

                portfolioItems.forEach(item => {
                    if (filterValue === 'all' || item.classList.contains(filterValue)) {
                        item.style.display = 'block';
                        setTimeout(() => {
                            item.style.opacity = '1';
                            item.style.transform = 'scale(1)';
                        }, 100);
                    } else {
                        item.style.opacity = '0';
                        item.style.transform = 'scale(0.8)';
                        setTimeout(() => {
                            item.style.display = 'none';
                        }, 300);
                    }
                });
            });
        });

        // Initialize first filter as active
        document.querySelector('.portfolio-filter[data-filter="all"]').classList.add('active', 'bg-accent', 'text-primary');

        // Animated counters
        function animateCounters() {
            const counters = document.querySelectorAll('.counter-number');
            counters.forEach(counter => {
                const target = parseInt(counter.getAttribute('data-target'));
                const duration = 2000;
                const increment = target / (duration / 16);
                let current = 0;

                const updateCounter = () => {
                    current += increment;
                    if (current < target) {
                        counter.textContent = Math.floor(current);
                        requestAnimationFrame(updateCounter);
                    } else {
                        counter.textContent = target;
                    }
                };

                updateCounter();
            });
        }

        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');

                    // Trigger counter animation for about section
                    if (entry.target.id === 'about') {
                        setTimeout(animateCounters, 500);
                    }
                }
            });
        }, observerOptions);

        // Observe all fade-in elements
        document.querySelectorAll('.fade-in, .fade-in-left, .fade-in-right').forEach(el => {
            observer.observe(el);
        });

        // Stagger animation for portfolio items
        const portfolioObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const stagger = entry.target.style.getPropertyValue('--stagger') || 0;
                    setTimeout(() => {
                        entry.target.classList.add('visible');
                    }, stagger * 100);
                }
            });
        }, observerOptions);

        document.querySelectorAll('.portfolio-item, .testimonial-card, .pricing-card').forEach(el => {
            portfolioObserver.observe(el);
        });

        // Form submission
        document.querySelector('form').addEventListener('submit', function(e) {
            e.preventDefault();

            // Show loading state
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;
            submitBtn.textContent = currentLang === 'ar' ? 'جاري الإرسال...' : 'Sending...';
            submitBtn.disabled = true;

            // Simulate form submission
            setTimeout(() => {
                alert(currentLang === 'ar' ? 'شكراً لرسالتك! سنتواصل معك قريباً.' : 'Thank you for your message! We\'ll get back to you soon.');
                this.reset();
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            }, 2000);
        });

        // Parallax effect for hero section
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const parallax = document.querySelector('.parallax');
            if (parallax) {
                const speed = scrolled * 0.5;
                parallax.style.transform = `translateY(${speed}px)`;
            }
        });

        // Loading animation
        window.addEventListener('load', () => {
            document.body.classList.add('loaded');

            // Trigger initial animations
            setTimeout(() => {
                document.querySelectorAll('.fade-in').forEach((el, index) => {
                    setTimeout(() => {
                        el.classList.add('visible');
                    }, index * 100);
                });
            }, 500);
        });

        // Initialize language on page load
        updateLanguage();
    </script>
</body>
</html>
